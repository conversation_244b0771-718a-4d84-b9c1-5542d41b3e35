'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Menu, 
  X, 
  BarChart3, 
  Package, 
  Folder, 
  ShoppingCart, 
  Users, 
  Star, 
  Ticket, 
  Settings,
  Home,
  Bell,
  Search,
  LogOut,
  User,
  ChevronDown
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuthStore } from '@/store/auth'
import { getAdminSidebarItems } from '@/lib/permissions'
import { APP_NAME } from '@/constants'
import { cn } from '@/lib/utils'

const iconMap = {
  BarChart3,
  Package,
  Folder,
  ShoppingCart,
  Users,
  Star,
  Ticket,
  Settings,
}

export function AdminLayout({ children }: { children: React.ReactNode }) {
  console.log('=== AdminLayout RENDERING ===')
  
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const { user, logout } = useAuthStore()

  console.log('AdminLayout - user:', user?.role, 'pathname:', pathname)

  const sidebarItems = user ? getAdminSidebarItems(user.role) : []

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/60 backdrop-blur-sm lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Enhanced Sidebar - Consistent with Customer Theme */}
      <div className={cn(
        'fixed inset-y-0 left-0 z-50 w-72 bg-gradient-to-b from-black via-gray-900 to-black shadow-2xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:transform-none flex flex-col border-r border-white/10',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        <div className="flex items-center justify-between h-20 px-6 border-b border-white/10 flex-shrink-0">
          <Link href="/admin" className="flex items-center space-x-3 group">
            <div className="h-10 w-10 rounded-xl bg-[#ff9000] flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
              <span className="text-white font-bold text-xl">B</span>
            </div>
            <div>
              <span className="text-xl font-bold text-white">BiHub Admin</span>
              <p className="text-xs text-gray-400">Dashboard</p>
            </div>
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden h-8 w-8 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <nav className="flex-1 mt-6 px-4 overflow-y-auto">
          {/* Back to Store */}
          <Link
            href="/"
            className="flex items-center px-3 py-2.5 text-sm font-medium text-gray-400 rounded-lg hover:bg-white/5 hover:text-white mb-6 transition-colors group"
          >
            <div className="w-7 h-7 rounded-lg bg-white/5 flex items-center justify-center mr-3 group-hover:bg-[#ff9000] transition-colors">
              <Home className="h-4 w-4" />
            </div>
            Back to Store
          </Link>

          {/* Navigation Items - Simplified */}
          <div className="space-y-2">
            {sidebarItems.map((item) => {
              const Icon = iconMap[item.icon as keyof typeof iconMap]

              // Fix active logic to avoid conflicts
              let isActive = false
              if (item.href === '/admin') {
                // Dashboard should only be active on exact /admin path
                isActive = pathname === '/admin'
              } else {
                // Other items should be active when pathname starts with their href
                isActive = pathname === item.href || pathname.startsWith(`${item.href}/`)
              }

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors group',
                    isActive
                      ? 'bg-[#ff9000] text-white'
                      : 'text-gray-400 hover:bg-white/5 hover:text-white'
                  )}
                >
                  <div className={cn(
                    'w-7 h-7 rounded-lg flex items-center justify-center mr-3 transition-colors',
                    isActive
                      ? 'bg-white/20 text-white'
                      : 'bg-white/5 group-hover:bg-[#ff9000] group-hover:text-white'
                  )}>
                    <Icon className="h-4 w-4" />
                  </div>
                  {item.label}
                </Link>
              )
            })}
          </div>
        </nav>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Enhanced Top bar - Consistent with Customer Theme */}
        <div className="sticky top-0 z-30 bg-black/90 backdrop-blur-xl border-b border-white/10">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden h-8 w-8 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white"
              >
                <Menu className="h-4 w-4" />
              </Button>

              <div>
                <h1 className="text-xl font-bold text-white">
                  {pathname === '/admin' ? 'Dashboard' :
                   pathname.split('/').pop()?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </h1>
                <p className="text-sm text-gray-400">
                  {pathname === '/admin' ? 'Overview of your store performance' : 'Manage your store'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Simplified Search */}
              <div className="hidden md:block">
                <div className="relative">
                  <Input
                    type="search"
                    placeholder="Search..."
                    className="w-64 h-9 pl-9 pr-4 rounded-lg border border-white/20 focus:border-[#ff9000] bg-white/5 text-white placeholder:text-gray-400"
                  />
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                </div>
              </div>

              {/* Simplified Notifications */}
              <Button variant="ghost" size="icon" className="relative h-9 w-9 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white">
                <Bell className="h-4 w-4" />
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-[#ff9000] rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">3</span>
                </div>
              </Button>

              {/* Simplified User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2 h-9 px-3 rounded-lg hover:bg-white/10 transition-colors group">
                    <div className="w-7 h-7 bg-[#ff9000] rounded-lg flex items-center justify-center">
                      <span className="text-white text-sm font-bold">
                        {user?.first_name?.[0]}{user?.last_name?.[0]}
                      </span>
                    </div>
                    <div className="hidden lg:block text-left">
                      <div className="text-sm font-medium text-white">
                        {user?.first_name} {user?.last_name}
                      </div>
                      <div className="text-xs text-gray-400 capitalize">
                        {user?.role.replace('_', ' ')}
                      </div>
                    </div>
                    <ChevronDown className="h-3 w-3 text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-64 p-2 border border-white/20 rounded-lg bg-black/90 backdrop-blur-xl">
                  <div className="p-3 border-b border-white/10">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-[#ff9000] rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold">
                          {user?.first_name?.[0]}{user?.last_name?.[0]}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-white">
                          {user?.first_name} {user?.last_name}
                        </p>
                        <p className="text-sm text-gray-400">{user?.email}</p>
                        <span className="text-xs text-gray-400 capitalize">
                          {user?.role.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="py-2">
                    <DropdownMenuItem asChild>
                      <Link href="/admin/profile" className="cursor-pointer flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-gray-800 transition-colors">
                        <div className="w-8 h-8 rounded-xl bg-blue-900/30 flex items-center justify-center">
                          <User className="h-4 w-4 text-blue-400" />
                        </div>
                        <div>
                          <p className="font-medium text-white">Profile</p>
                          <p className="text-xs text-gray-400">Manage your account</p>
                        </div>
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link href="/admin/settings" className="cursor-pointer flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-gray-800 transition-colors">
                        <div className="w-8 h-8 rounded-xl bg-purple-900/30 flex items-center justify-center">
                          <Settings className="h-4 w-4 text-purple-400" />
                        </div>
                        <div>
                          <p className="font-medium text-white">Settings</p>
                          <p className="text-xs text-gray-400">Preferences & config</p>
                        </div>
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link href="/" className="cursor-pointer flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-gray-800 transition-colors">
                        <div className="w-8 h-8 rounded-xl bg-emerald-900/30 flex items-center justify-center">
                          <Home className="h-4 w-4 text-emerald-400" />
                        </div>
                        <div>
                          <p className="font-medium text-white">Back to Store</p>
                          <p className="text-xs text-gray-400">Visit your store</p>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  </div>

                  <div className="pt-2 border-t border-gray-700/30">
                    <DropdownMenuItem
                      className="cursor-pointer flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-red-900/20 text-red-400 focus:text-red-400 transition-colors"
                      onClick={() => logout()}
                    >
                      <div className="w-8 h-8 rounded-xl bg-red-900/30 flex items-center justify-center">
                        <LogOut className="h-4 w-4 text-red-400" />
                      </div>
                      <div>
                        <p className="font-medium">Sign out</p>
                        <p className="text-xs text-gray-400">End your session</p>
                      </div>
                    </DropdownMenuItem>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Enhanced Page content */}
        <main className="p-8 bg-gradient-to-br from-slate-950 via-slate-900/10 to-slate-950 min-h-screen">
          {children}
        </main>
      </div>
    </div>
  )
}
