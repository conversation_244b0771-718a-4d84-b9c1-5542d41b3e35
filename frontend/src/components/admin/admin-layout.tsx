'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Menu,
  X,
  BarChart3,
  Package,
  Folder,
  ShoppingCart,
  Users,
  Star,
  Ticket,
  Settings,
  Home,
  Bell,
  Search,
  LogOut,
  User,
  ChevronDown,
  ArrowLeft
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuthStore } from '@/store/auth'
import { getAdminSidebarItems } from '@/lib/permissions'
import { APP_NAME } from '@/constants'
import { cn } from '@/lib/utils'
import { AnimatedBackground } from '@/components/ui/animated-background'

const iconMap = {
  BarChart3,
  Package,
  Folder,
  ShoppingCart,
  <PERSON>,
  Star,
  Ticket,
  Settings,
}

export function AdminLayout({ children }: { children: React.ReactNode }) {
  console.log('=== AdminLayout RENDERING ===')

  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const { user, logout } = useAuthStore()

  console.log('AdminLayout - user:', user?.role, 'pathname:', pathname)

  const sidebarItems = user ? getAdminSidebarItems(user.role) : []

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white relative overflow-hidden">
      <AnimatedBackground className="opacity-30" />

      <div className="relative z-10 flex">
        {/* Mobile sidebar backdrop */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black/60 backdrop-blur-sm lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Admin Sidebar - Consistent with Customer Theme */}
        <div className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 bg-gradient-to-b from-black via-gray-900 to-black backdrop-blur-xl border-r border-white/10 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:transform-none flex flex-col',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        )}>
          {/* Admin Header */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-white/10 flex-shrink-0">
            <Link href="/admin" className="flex items-center space-x-2 group">
              <div className="h-8 w-8 rounded-lg bg-[#ff9000] flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                <span className="text-white font-bold text-lg">B</span>
              </div>
              <div>
                <span className="text-lg font-bold text-white">BiHub</span>
                <p className="text-xs text-gray-400">Admin</p>
              </div>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden h-8 w-8 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 mt-4 px-3 overflow-y-auto">
            {/* Back to Store */}
            <Link
              href="/"
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-400 rounded-lg hover:bg-white/5 hover:text-white mb-4 transition-colors group"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Store
            </Link>

            {/* Navigation Items */}
            <div className="space-y-1">
              {sidebarItems.map((item) => {
                const Icon = iconMap[item.icon as keyof typeof iconMap]

                // Fix active logic to avoid conflicts
                let isActive = false
                if (item.href === '/admin') {
                  // Dashboard should only be active on exact /admin path
                  isActive = pathname === '/admin'
                } else {
                  // Other items should be active when pathname starts with their href
                  isActive = pathname === item.href || pathname.startsWith(`${item.href}/`)
                }

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                      isActive
                        ? 'bg-[#ff9000] text-white'
                        : 'text-gray-400 hover:bg-white/5 hover:text-white'
                    )}
                  >
                    <Icon className="h-4 w-4 mr-3" />
                    {item.label}
                  </Link>
                )
              })}
            </div>
          </nav>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Top Header - Consistent with Customer Theme */}
          <header className="sticky top-0 z-30 bg-gradient-to-r from-black/95 via-gray-900/95 to-black/95 backdrop-blur-xl border-b border-white/10">
            <div className="container mx-auto px-4 lg:px-6 xl:px-8">
              <div className="flex items-center justify-between h-16">
                <div className="flex items-center space-x-4">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSidebarOpen(true)}
                    className="lg:hidden h-8 w-8 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white"
                  >
                    <Menu className="h-4 w-4" />
                  </Button>

                  <div>
                    <h1 className="text-xl font-bold text-white">
                      {pathname === '/admin' ? 'Dashboard' :
                       pathname.split('/').pop()?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </h1>
                    <p className="text-sm text-gray-400">
                      {pathname === '/admin' ? 'Overview of your store performance' : 'Manage your store'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Search */}
                  <div className="hidden md:block">
                    <div className="relative">
                      <Input
                        type="search"
                        placeholder="Search admin..."
                        className="w-64 h-9 pl-9 pr-4 rounded-lg border border-white/20 focus:border-[#ff9000] bg-white/5 text-white placeholder:text-gray-400"
                      />
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                  </div>

                  {/* Notifications */}
                  <Button variant="ghost" size="icon" className="relative h-9 w-9 rounded-lg hover:bg-white/10 text-gray-400 hover:text-white">
                    <Bell className="h-4 w-4" />
                    <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 bg-[#ff9000] text-white text-xs flex items-center justify-center">
                      3
                    </Badge>
                  </Button>

                  {/* User Menu */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="flex items-center space-x-2 h-9 px-3 rounded-lg hover:bg-white/10 transition-colors">
                        <div className="w-7 h-7 bg-[#ff9000] rounded-lg flex items-center justify-center">
                          <span className="text-white text-sm font-bold">
                            {user?.first_name?.[0]}{user?.last_name?.[0]}
                          </span>
                        </div>
                        <div className="hidden lg:block text-left">
                          <div className="text-sm font-medium text-white">
                            {user?.first_name} {user?.last_name}
                          </div>
                          <div className="text-xs text-gray-400 capitalize">
                            {user?.role.replace('_', ' ')}
                          </div>
                        </div>
                        <ChevronDown className="h-3 w-3 text-gray-400" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56 border border-white/20 rounded-lg bg-black/90 backdrop-blur-xl">
                      <DropdownMenuLabel className="px-3 py-2">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-[#ff9000] rounded-lg flex items-center justify-center">
                            <span className="text-white text-sm font-bold">
                              {user?.first_name?.[0]}{user?.last_name?.[0]}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-white">
                              {user?.first_name} {user?.last_name}
                            </p>
                            <p className="text-xs text-gray-400">{user?.email}</p>
                          </div>
                        </div>
                      </DropdownMenuLabel>

                      <DropdownMenuSeparator className="bg-white/10" />

                      <DropdownMenuItem asChild>
                        <Link href="/admin/settings" className="flex items-center gap-2 px-3 py-2 text-sm text-white hover:bg-white/10">
                          <Settings className="h-4 w-4" />
                          Settings
                        </Link>
                      </DropdownMenuItem>

                      <DropdownMenuItem asChild>
                        <Link href="/" className="flex items-center gap-2 px-3 py-2 text-sm text-white hover:bg-white/10">
                          <Home className="h-4 w-4" />
                          Back to Store
                        </Link>
                      </DropdownMenuItem>

                      <DropdownMenuSeparator className="bg-white/10" />

                      <DropdownMenuItem
                        className="flex items-center gap-2 px-3 py-2 text-sm text-red-400 hover:bg-red-500/10 focus:text-red-400"
                        onClick={() => logout()}
                      >
                        <LogOut className="h-4 w-4" />
                        Sign out
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          </header>

          {/* Main Content - Consistent with Customer Theme */}
          <main className="flex-1 container mx-auto px-4 lg:px-6 xl:px-8 py-6">
            <div className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] backdrop-blur-xl border border-white/10 rounded-xl p-6 min-h-[calc(100vh-8rem)]">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
